#!/usr/bin/env python3
"""
Simple syntax test for the exchange account manager changes
"""

import ast
import sys

def test_syntax(filename):
    """Test if a Python file has valid syntax"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Parse the AST to check syntax
        ast.parse(source)
        print(f"✅ Syntax OK: {filename}")
        return True
    except SyntaxError as e:
        print(f"❌ Syntax Error in {filename}:")
        print(f"   Line {e.lineno}: {e.text}")
        print(f"   {' ' * (e.offset - 1)}^")
        print(f"   {e.msg}")
        return False
    except Exception as e:
        print(f"❌ Error reading {filename}: {e}")
        return False

if __name__ == "__main__":
    filename = "dash_app/widgets/exchange_account_manager.py"
    success = test_syntax(filename)
    sys.exit(0 if success else 1)
