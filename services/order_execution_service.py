"""
Order Execution Service

Handles the execution of orders from SETUP status trades, converting them to live orders
on Bybit and managing the trade lifecycle from setup to execution.
"""

import logging
import time
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timezone

from services.bybit import Bybit
from models.trade import Trade, TradeStatus, TradeDirection
from models.order import Order, BuySell
from models.exchange_connection import ExchangeConnection
from services.trade_creation_service import OrderType, OrderStatus
from trades_db import TradesDB, get_db_cursor, get_db_connection

logger = logging.getLogger(__name__)


def calculate_trigger_direction(order_type: str, position_direction: str) -> int:
    """
    Calculate triggerDirection for Bybit stop/take-profit orders.

    Args:
        order_type: "Stop" or "TakeProfit"
        position_direction: "Long" or "Short" (the position direction)

    Returns:
        int: 1 for Rising (above current price), 2 for Falling (below current price)
    """
    if order_type == "Stop":
        # Stop-loss orders (close position when price moves against you)
        if position_direction == "Long":
            return 2  # Falling - trigger when price falls below trigger price
        else:  # Short position
            return 1  # Rising - trigger when price rises above trigger price

    elif order_type == "TakeProfit":
        # Take-profit orders (close position when price moves in your favor)
        if position_direction == "Long":
            return 1  # Rising - trigger when price rises above trigger price
        else:  # Short position
            return 2  # Falling - trigger when price falls below trigger price

    return 1  # Default fallback


class OrderExecutionResult:
    """Result of order execution attempt"""
    def __init__(self, success: bool, order_id: str = None, error: str = None, 
                 bybit_response: dict = None):
        self.success = success
        self.order_id = order_id
        self.error = error
        self.bybit_response = bybit_response
        self.timestamp = datetime.now(timezone.utc)


class TradeExecutionResult:
    """Result of trade execution attempt"""
    def __init__(self, trade_id: str, success: bool, executed_orders: List[OrderExecutionResult] = None,
                 failed_orders: List[OrderExecutionResult] = None, error: str = None):
        self.trade_id = trade_id
        self.success = success
        self.executed_orders = executed_orders or []
        self.failed_orders = failed_orders or []
        self.error = error
        self.timestamp = datetime.now(timezone.utc)


class OrderExecutionService:
    """Service for executing SETUP trades on Bybit"""

    @staticmethod
    def execute_setup_trade(trade: Trade, connection: ExchangeConnection = None, mock_execution: bool = False) -> TradeExecutionResult:
        """
        Execute all orders in a SETUP trade on Bybit using session-based monitoring.

        Args:
            trade: Trade object with SETUP status
            connection: ExchangeConnection with user credentials for API calls
            mock_execution: If True, bypasses actual order placement and returns mock success responses

        Returns:
            TradeExecutionResult: Execution results with success/failure details
        """
        if trade.status != TradeStatus.SETUP:
            return TradeExecutionResult(
                trade_id=trade.exchange_trade_id,
                success=False,
                error=f"Trade status is {trade.status.value}, expected SETUP"
            )

        if not connection:
            return TradeExecutionResult(
                trade_id=trade.exchange_trade_id,
                success=False,
                error="ExchangeConnection required for trade execution"
            )

        if mock_execution:
            logger.info(f"🎭 Mock execution mode enabled for trade {trade.exchange_trade_id}")

        logger.info(f"🚀 Executing SETUP trade {trade.exchange_trade_id} with {len(trade.trade_orders)} orders using {connection.exchange_name} credentials")

        executed_orders = []
        failed_orders = []
        session_id = None

        # Group orders by execution priority
        # 1. Entry orders first
        # 2. Stop-loss and take-profit orders after entry
        entry_orders = [o for o in trade.trade_orders if o.orderType == OrderType.ENTRY]
        stop_loss_orders = [o for o in trade.trade_orders if o.orderType == OrderType.STOP_LOSS]
        take_profit_orders = [o for o in trade.trade_orders if o.orderType == OrderType.TAKE_PROFIT]
        position_direction = "Long" if trade.direction == TradeDirection.LONG else "Short"

        try:
            # Start WebSocket monitoring session for real-time updates (skip in mock mode)
            if not mock_execution:
                session_id = OrderExecutionService._start_monitoring_session(connection)
                if session_id:
                    logger.info(f"📡 Started monitoring session {session_id} for trade {trade.exchange_trade_id}")
            else:
                logger.info(f"🎭 Skipping monitoring session setup in mock execution mode")
        except Exception as e:
            logger.warning(f"⚠️ Could not start monitoring session: {e}")
            # Continue without monitoring - not critical for execution

        try:
            # Execute entry orders first
            for order in entry_orders:
                result = OrderExecutionService._execute_order(order, position_direction, connection, mock_execution)
                if result.success:
                    executed_orders.append(result)
                    logger.info(f"✅ Entry order executed: {result.order_id}")
                else:
                    failed_orders.append(result)
                    logger.error(f"❌ Entry order failed: {result.error}")

            # If any entry orders succeeded, execute stop-loss and take-profit
            if executed_orders:
                # Execute stop-loss orders
                for order in stop_loss_orders:
                    result = OrderExecutionService._execute_order(order, position_direction, connection, mock_execution)
                    if result.success:
                        executed_orders.append(result)
                        logger.info(f"✅ Stop-loss order executed: {result.order_id}")
                    else:
                        failed_orders.append(result)
                        logger.error(f"❌ Stop-loss order failed: {result.error}")

                # Execute take-profit orders
                for order in take_profit_orders:
                    result = OrderExecutionService._execute_order(order, position_direction, connection, mock_execution)
                    if result.success:
                        executed_orders.append(result)
                        logger.info(f"✅ Take-profit order executed: {result.order_id}")
                    else:
                        failed_orders.append(result)
                        logger.error(f"❌ Take-profit order failed: {result.error}")

            # Update trade status based on execution results
            if executed_orders and not failed_orders:
                # All orders executed successfully
                trade.status = TradeStatus.OPEN
                success = True
                error = None
            elif executed_orders:
                # Partial execution
                trade.status = TradeStatus.OPEN  # Still open with some orders executed
                success = True
                error = f"Partial execution: {len(executed_orders)} succeeded, {len(failed_orders)} failed"
            else:
                # No orders executed
                success = False
                error = "No orders were successfully executed"

            # Save updated trade to database
            if executed_orders:
                cursor = get_db_cursor()
                TradesDB.saveTrade(trade, cursor)
                get_db_connection().commit()

            # Add executed order IDs to monitoring session
            if executed_orders and session_id:
                OrderExecutionService._add_orders_to_session(session_id, executed_orders)

            return TradeExecutionResult(
                trade_id=trade.exchange_trade_id,
                success=success,
                executed_orders=executed_orders,
                failed_orders=failed_orders,
                error=error
            )

        except Exception as e:
            logger.error(f"❌ Unexpected error executing trade {trade.exchange_trade_id}: {e}")
            return TradeExecutionResult(
                trade_id=trade.exchange_trade_id,
                success=False,
                error=f"Unexpected error: {str(e)}"
            )
        finally:
            # Keep session alive for real-time monitoring unless execution completely failed
            if not executed_orders and session_id:
                try:
                    OrderExecutionService._stop_monitoring_session(session_id)
                    logger.info(f"🛑 Stopped monitoring session {session_id} (no orders executed)")
                except Exception as e:
                    logger.warning(f"⚠️ Could not stop monitoring session: {e}")

    @staticmethod
    def _execute_order(order: Order, position_direction: str, connection: ExchangeConnection, mock_execution: bool = False) -> OrderExecutionResult:
        """
        Execute a single order on Bybit using per-request credentials.

        Args:
            order: Order object to execute
            position_direction: "Long" or "Short" for trigger direction calculation
            connection: ExchangeConnection with user credentials
            mock_execution: If True, bypasses actual order placement and returns mock success response

        Returns:
            OrderExecutionResult: Execution result
        """
        # Handle mock execution mode
        if mock_execution:
            logger.info(f"🎭 Mock execution: Simulating order placement for {order.orderType.value} order {order.order_id}")

            # Generate a mock order ID
            import uuid
            mock_order_id = f"mock_{uuid.uuid4().hex[:8]}"

            # Update order status to simulate successful placement
            order.orderStatus = OrderStatus.PENDING
            order.order_id = mock_order_id

            # Return mock success response
            mock_response = {
                "retCode": 0,
                "retMsg": "OK",
                "result": {
                    "orderId": mock_order_id,
                    "orderLinkId": f"{order.order_id}",
                    "symbol": order.symbol,
                    "side": "Buy" if order.buySell == BuySell.BUY else "Sell",
                    "qty": str(order.quantity),
                    "orderStatus": "New"
                }
            }

            return OrderExecutionResult(
                success=True,
                order_id=mock_order_id,
                bybit_response=mock_response
            )

        try:
            # Convert TradeCraft order to Bybit parameters
            symbol = order.symbol
            side = "Buy" if order.buySell == BuySell.BUY else "Sell"
            qty = str(order.quantity)
            order_link_id = f"{order.order_id}"

            # Execute based on order type using connection credentials
            if order.orderType == OrderType.ENTRY:
                if order.price and order.price > 0:
                    # Limit entry order
                    response = Bybit.submit_order(
                        connection=connection,
                        symbol=symbol,
                        side=side,
                        order_type="Limit",
                        qty=qty,
                        price=str(order.price),
                        order_link_id=order_link_id
                    )
                else:
                    # Market entry order
                    response = Bybit.submit_order(
                        connection=connection,
                        symbol=symbol,
                        side=side,
                        order_type="Market",
                        qty=qty,
                        order_link_id=order_link_id
                    )

            elif order.orderType == OrderType.STOP_LOSS:
                # Stop-loss order (opposite side of entry to close position)
                stop_side = "Buy" if side == "Sell" else "Sell"
                trigger_direction = calculate_trigger_direction("Stop", position_direction)

                response = Bybit.submit_stop_loss(
                    connection=connection,
                    symbol=symbol,
                    side=stop_side,
                    qty=qty,
                    trigger_price=str(order.price),
                    trigger_direction=trigger_direction,
                    order_link_id=order_link_id
                )

            elif order.orderType == OrderType.TAKE_PROFIT:
                # Take-profit order (opposite side of entry to close position)
                tp_side = "Buy" if side == "Sell" else "Sell"
                trigger_direction = calculate_trigger_direction("TakeProfit", position_direction)

                response = Bybit.submit_take_profit(
                    connection=connection,
                    symbol=symbol,
                    side=tp_side,
                    qty=qty,
                    trigger_price=str(order.price),
                    trigger_direction=trigger_direction,
                    order_link_id=order_link_id
                )

            else:
                return OrderExecutionResult(
                    success=False,
                    error=f"Unsupported order type: {order.orderType}"
                )

            # Check if response indicates success
            if response and response.get("retCode") == 0:
                result = response.get("result", {})
                bybit_order_id = result.get("orderId")
                
                # Update order status
                order.orderStatus = OrderStatus.PENDING
                order.order_id = bybit_order_id or order.order_id
                
                return OrderExecutionResult(
                    success=True,
                    order_id=bybit_order_id,
                    bybit_response=response
                )
            else:
                error_msg = response.get("retMsg", "Unknown error") if response else "No response"
                return OrderExecutionResult(
                    success=False,
                    error=f"Bybit API error: {error_msg}",
                    bybit_response=response
                )

        except Exception as e:
            logger.error(f"❌ Error executing order {order.order_id}: {e}")
            return OrderExecutionResult(
                success=False,
                error=f"Execution error: {str(e)}"
            )

    @staticmethod
    def _start_monitoring_session(connection: ExchangeConnection) -> Optional[str]:
        """Start a WebSocket monitoring session for real-time updates"""
        try:
            from services.bybit import Bybit
            return Bybit.start_monitoring_session(connection)
        except Exception as e:
            logger.warning(f"⚠️ Could not start monitoring session: {e}")
            return None

    @staticmethod
    def _add_orders_to_session(session_id: str, executed_orders: List[OrderExecutionResult]):
        """Add executed order IDs to monitoring session (placeholder for future enhancement)"""
        try:
            # Extract order IDs from execution results
            order_ids = []
            for result in executed_orders:
                if result.success and result.order_id:
                    order_ids.append(result.order_id)

            if order_ids:
                logger.info(f"📊 Would monitor {len(order_ids)} orders in session {session_id}: {order_ids}")
                # Future enhancement: Add endpoint to proxy for adding specific order IDs to monitor

        except Exception as e:
            logger.warning(f"⚠️ Could not add orders to monitoring session: {e}")

    @staticmethod
    def _stop_monitoring_session(session_id: str):
        """Stop a WebSocket monitoring session"""
        try:
            from services.bybit import Bybit
            Bybit.stop_monitoring_session(session_id)
        except Exception as e:
            logger.warning(f"⚠️ Could not stop monitoring session: {e}")
