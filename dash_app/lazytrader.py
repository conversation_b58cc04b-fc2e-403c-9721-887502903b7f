# Minimal app to test the standalone charting.html
import os
import threading
import time

import dash
import dash_bootstrap_components as dbc
import uvicorn
from dash import Dash, html, Input, Output, callback, dcc, State, ALL
from dash_extensions import EventListener
from flask import session

from models.exchange_connection import ExchangeConnection
from services.user_balance_service import UserBalanceService
# Import database and models for exchange connections
from trades_db import ExchangeConnectionDB, TradesDB

# Default theme
DEFAULT_THEME = dbc.themes.COSMO
external_stylesheets = [DEFAULT_THEME]

# Only include the IFrame page; let the iframe load all its own scripts.
app = Dash(
    __name__,
    external_stylesheets=external_stylesheets,
    # Prevent the charting asset scripts from auto-running in this document;
    # they will still be served under /assets and used by the iframe.
    assets_ignore=r"^(?!.*iframe-autoheight-listener\.js$).*\.js$",
    # ignore all JS except iframe-autoheight-listener.js
    suppress_callback_exceptions=True  # Allow callbacks for dynamically generated components
)


def get_trade_setups():
    """Fetch trade setups from database"""
    try:
        from models.trade import TradeStatus, TradeSource
        from services.trade_creation_service import OrderType
        import helper

        # Query database for SETUP trades with SYSTEM_FIBONACCI source
        all_trades = TradesDB.get_trades()
        setup_trades = [
            trade for trade in all_trades
            if (trade.status == TradeStatus.OPEN or trade.status == TradeStatus.SETUP) and trade.source == TradeSource.SYSTEM_FIBONACCI
        ]

        if not setup_trades:
            return {"setups": []}

        formatted_setups = []

        for trade in setup_trades:
            try:
                # Extract order data by type
                stop_loss_orders = [o for o in trade.trade_orders if o.orderType == OrderType.STOP_LOSS]
                entry_orders = [o for o in trade.trade_orders if o.orderType == OrderType.ENTRY]
                take_profit_orders = [o for o in trade.trade_orders if o.orderType == OrderType.TAKE_PROFIT]

                # Get P1 (stop loss price) and P2 (highest entry price for direction determination)
                p1 = float(stop_loss_orders[0].price) if stop_loss_orders else 0.0

                # For P2, use the entry price that's furthest from stop loss
                p2 = 0.0
                if entry_orders:
                    entry_prices = [float(o.price) for o in entry_orders]
                    # P2 is the entry furthest from stop loss
                    p2 = max(entry_prices) if p1 < min(entry_prices) else min(entry_prices)

                # Format entry levels
                entry_levels = "No entries"
                if entry_orders:
                    entry_prices = [f"{float(o.price):.2f}" for o in entry_orders]
                    entry_levels = ", ".join(entry_prices)

                # Format take profit levels
                tp_levels = "No TPs"
                if take_profit_orders:
                    tp_prices = [f"{float(o.price):.2f}" for o in take_profit_orders]
                    tp_levels = ", ".join(tp_prices)

                # Format timestamp
                timestamp = "Unknown"
                if trade.timeOpen:
                    timestamp = helper.formatDate(trade.timeOpen, date_format='%Y-%m-%d %H:%M:%S')

                # Get exchange name
                exchange = "Unknown"
                if hasattr(trade, 'exchange') and trade.exchange:
                    exchange = trade.exchange.value

                # Get timeframe
                timeframe = "Unknown"
                if hasattr(trade, 'time_frame') and trade.time_frame:
                    timeframe = trade.time_frame

                setup_data = {
                    "symbol": trade.symbol or "Unknown",
                    "direction": trade.direction.value if hasattr(trade.direction, 'value') else str(trade.direction),
                    "type": "Fibonacci",
                    "p1": p1,
                    "p2": p2,
                    "entry_levels": entry_levels,
                    "tp_levels": tp_levels,
                    "timestamp": timestamp,
                    "exchange": exchange,
                    "timeframe": timeframe
                }

                formatted_setups.append(setup_data)

            except Exception as e:
                print(f"Error formatting trade setup {trade.id_field}: {e}")
                continue

        print(f"✅ Retrieved {len(formatted_setups)} trade setups from database")
        return {"setups": formatted_setups}

    except Exception as e:
        print(f"❌ Error retrieving trade setups: {e}")
        return {"setups": []}


page_layout = html.Div([
    # EventListener to catch custom events from the iframe
    EventListener(
        events=[{"event": "fib_completed", "props": ["detail"]}],
        logging=True,
        id="fib-event-listener"
    ),
    html.Iframe(
        src="/assets/charting/charting.html",  # absolute path to Dash assets
        style={"width": "100%", "height": "30px", "border": "0"},
        id="chart-iframe"
    ),

    # Account Section
    dbc.Container([
        dbc.Card([
            dbc.CardHeader([
                html.Div([
                    html.I(className="bi bi-wallet2 me-2", style={"fontSize": "1.2rem", "color": "#0d6efd"}),
                    html.H5("Account Settings", className="mb-0")
                ], className="d-flex align-items-center")
            ]),
            dbc.CardBody([
                dbc.Row([
                    # Exchange Selection
                    dbc.Col([
                        dbc.Label("Exchange", className="fw-bold mb-2"),
                        dcc.Dropdown(
                            id="exchange-dropdown",
                            options=[],  # Will be populated dynamically
                            value=None,
                            clearable=False,
                            placeholder="Select exchange...",
                            style={"fontSize": "14px"}
                        )
                    ], width=4),

                    # Wallet Balance Display
                    dbc.Col([
                        dbc.Label("Wallet Balance", className="fw-bold mb-2"),
                        html.Div([
                            html.Div(id="balance-display-content", children=[
                                html.Span("Select exchange", className="text-muted")
                            ])
                        ], className="p-2 bg-light rounded border", id="balance-container"),
                        # Refresh button
                        dbc.Button([
                            html.I(className="bi bi-arrow-clockwise me-1"),
                            "Refresh"
                        ], id="refresh-balance-btn", color="outline-primary", size="sm",
                            className="mt-2", disabled=True)
                    ], width=4),

                    # Risk Percentage Slider
                    dbc.Col([
                        dbc.Label("Risk Per Trade", className="fw-bold mb-2"),
                        html.Div([
                            dcc.Slider(
                                id="risk-slider",
                                min=0.5,
                                max=3.0,
                                step=0.1,
                                value=1.0,
                                marks={
                                    0.5: {"label": "0.5%", "style": {"fontSize": "12px"}},
                                    1.0: {"label": "1%", "style": {"fontSize": "12px"}},
                                    2.0: {"label": "2%", "style": {"fontSize": "12px"}},
                                    3.0: {"label": "3%", "style": {"fontSize": "12px"}}
                                },
                                tooltip={"placement": "bottom", "always_visible": True}
                            ),
                            html.Div(id="risk-display", className="text-center mt-2",
                                     style={"fontSize": "14px", "fontWeight": "500"})
                        ])
                    ], width=4)
                ], className="align-items-end")
            ])
        ], className="shadow-sm mb-4")
    ], fluid=True, className="px-3 py-2"),

    # Hidden div to store fib data
    html.Div(id="fib-data-output", style={"display": "none"}),

    # Trade Components Section - Two Column Layout
    dbc.Container([
        dbc.Row([
            # Trade Setups Column (Left)
            dbc.Col([
                html.Div(id="trade-setups-container", children=[
                    dbc.Card([
                        dbc.CardHeader([
                            html.Div([
                                html.I(className="bi bi-clipboard-data me-2",
                                       style={"fontSize": "1.2rem", "color": "#0d6efd"}),
                                html.H5("Trade Setups", className="mb-0 me-2"),
                                dbc.Badge("0", color="primary", className="badge-pulse")
                            ], className="d-flex align-items-center")
                        ], className="trade-setups-header"),
                        dbc.CardBody([
                            html.Div([
                                html.Div([
                                    html.I(className="bi bi-lightbulb",
                                           style={"fontSize": "3rem", "color": "#6c757d", "opacity": "0.3"}),
                                ], className="text-center mb-3"),
                                html.H6("No Trade Setups Yet",
                                        className="text-muted text-center mb-2",
                                        style={"fontWeight": "500"}),
                                html.P([
                                    "Draw a Fibonacci retracement on the chart to create your first setup.",
                                ], className="text-muted text-center mb-0",
                                   style={"fontSize": "0.9rem", "lineHeight": "1.4"})
                            ], className="py-4")
                        ])
                    ], className="trade-setups-card shadow-sm")
                ])
            ], width=6, className="pe-2"),

            # Trade Details Column (Right)
            dbc.Col([
                html.Div(id="trade-details-container", children=[
                    dbc.Card([
                        dbc.CardBody([
                            html.Div([
                                html.I(className="bi bi-target me-2", style={"fontSize": "1.2rem", "color": "#6c757d"}),
                                html.H5("Trade Details", className="mb-0 text-muted")
                            ], className="d-flex align-items-center text-center"),
                            html.P("Draw a Fibonacci retracement to generate trade plan",
                                   className="text-muted text-center mt-3 mb-0")
                        ])
                    ], className="shadow-sm")
                ])
            ], width=6, className="ps-2")
        ])
    ], fluid=True, className="px-3 py-2"),

    # Store components for account data
    dcc.Store(id="account-data-store", data={"exchange": None, "balance": 0.00, "risk": 1.0, "connection_id": None}),
    dcc.Store(id="trade-details-store", data={}),
    dcc.Store(id="trade-setups-store", data=get_trade_setups()),
    dcc.Store(id="executed-orders-store", data=None),
    html.Div(id="order-dispatch-sink", style={"display": "none"}),
    dcc.Interval(id="balance-update-interval", interval=60000, n_intervals=0)  # Update every 60 seconds
])

app.layout = page_layout


# ================================
# Balance Fetching Functions
# ================================
def get_callbacks(app: dash.Dash) -> None:
    def get_exchange_balance(connection: ExchangeConnection) -> dict:
        """
        Fetch balance for a specific exchange connection using the UserBalanceService.

        Args:
            connection: ExchangeConnection instance with API credentials

        Returns:
            dict: {"success": bool, "balance": float, "currency": str, "error": str}
        """
        return UserBalanceService.get_balance(connection)

    # ================================
    # Dash Callbacks
    # ================================

    @app.callback(
        [Output("exchange-dropdown", "options"),
         Output("exchange-dropdown", "value")],
        [Input("exchange-dropdown", "id")],  # Trigger on component load
        prevent_initial_call=False
    )
    def populate_exchange_dropdown(_):
        session["user_id"] = 1
        """Populate exchange dropdown with user's connected exchanges"""
        # Check if user is logged in
        if "user_id" not in session:
            return [], None

        user_id = session["user_id"]

        try:
            # Load user's exchange connections
            user_connections = ExchangeConnectionDB.get_user_exchange_connections(user_id)

            if not user_connections:
                return [{"label": "No exchanges connected", "value": None, "disabled": True}], None

            # Create dropdown options with exchange icons
            exchange_icons = {
                "bybit": "🟡",
                "coinbase": "🔵",
                "binance": "🟠",
                "bitget": "🟢",
                "hyperliquid": "🔴",
                "blofin": "🟣",
                "woox": "⚫",
                "phemex": "🟤",
                "deribit": "🔶"
            }

            options = []
            default_value = None

            for conn in user_connections:
                icon = exchange_icons.get(conn.exchange_name.lower(), "⚪")
                status_indicator = "✅" if conn.connection_status == "connected" else "⏳" if conn.connection_status == "pending" else "❌"

                label = f"{icon} {conn.exchange_name.title()} - {conn.account_name} {status_indicator}"
                options.append({
                    "label": label,
                    "value": conn.id,  # Use connection ID as value
                    "disabled": conn.connection_status != "connected"  # Only allow connected exchanges
                })

                # Set default value to first connected exchange (only if not already set)
                if conn.connection_status == "connected" and default_value is None:
                    default_value = conn.id

            return options, default_value

        except Exception as e:
            print(f"❌ Error loading exchange connections: {e}")
            return [{"label": "Error loading exchanges", "value": None, "disabled": True}], None

    @app.callback(
        [Output("balance-display-content", "children"),
         Output("refresh-balance-btn", "disabled"),
         Output("account-data-store", "data", allow_duplicate=True)],
        [Input("exchange-dropdown", "value"),
         Input("refresh-balance-btn", "n_clicks")],
        [State("account-data-store", "data")],
        prevent_initial_call=True
    )
    def update_balance_display(selected_connection_id, refresh_clicks, current_data):
        """Update balance display when exchange is selected or refresh is clicked"""
        if not selected_connection_id:
            return [html.Span("Select exchange", className="text-muted")], True, current_data

        # Check if user is logged in
        if "user_id" not in session:
            return [html.Span("Please log in", className="text-danger")], True, current_data

        user_id = session["user_id"]

        try:
            # Get the connection details
            connection = ExchangeConnectionDB.get_exchange_connection_by_id(selected_connection_id)

            if not connection or connection.user_id != user_id:
                return [html.Span("Invalid connection", className="text-danger")], True, current_data

            # Show loading state
            if dash.callback_context.triggered_id == "refresh-balance-btn":
                # Show loading spinner during refresh
                loading_content = [
                    html.Span(dbc.Spinner(size="sm"), className="me-2"),
                    html.Span("Refreshing...", className="text-muted")
                ]
            else:
                # Show loading for initial load
                loading_content = [
                    html.Span(dbc.Spinner(size="sm"), className="me-2"),
                    html.Span("Loading balance...", className="text-muted")
                ]

            # Fetch balance
            balance_result = get_exchange_balance(connection)

            if balance_result["success"]:
                # Success - show balance
                balance_content = [
                    html.Span("$", className="text-muted me-1"),
                    html.Span(f"{balance_result['balance']:,.2f}",
                              className="fw-bold", style={"fontSize": "1.1rem"}),
                    html.Small(f" {balance_result['currency']}", className="text-muted ms-1")
                ]

                # Update account data store
                updated_data = current_data.copy()
                updated_data["exchange"] = connection.exchange_name
                updated_data["balance"] = balance_result["balance"]
                updated_data["connection_id"] = selected_connection_id

                return balance_content, False, updated_data
            else:
                # Error - show error message
                error_content = [
                    html.I(className="bi bi-exclamation-triangle text-warning me-2"),
                    html.Span(balance_result["error"], className="text-warning", style={"fontSize": "0.9rem"})
                ]

                return error_content, False, current_data

        except Exception as e:
            print(f"❌ Error updating balance display: {e}")
            error_content = [
                html.I(className="bi bi-exclamation-triangle text-danger me-2"),
                html.Span(f"Error: {str(e)}", className="text-danger", style={"fontSize": "0.9rem"})
            ]
            return error_content, False, current_data

    @app.callback(
        Output({"type": "trade-status", "index": ALL}, "children"),
        Output("executed-orders-store", "data", allow_duplicate=True),
        Input({"type": "play-trade-btn", "index": ALL}, "n_clicks"),
        State("trade-details-store", "data"),
        State("account-data-store", "data"),
        prevent_initial_call=True
    )
    def handle_play_trade_secure(n_clicks_list, fib_data, account_data):
        """Handle trade execution server-side in Dash with per-user credentials"""
        # Check if any button was clicked
        if not any(n_clicks_list) or not fib_data:
            return [dash.no_update] * len(n_clicks_list), dash.no_update

        # Find which button was clicked
        ctx = dash.callback_context
        if not ctx.triggered:
            return [dash.no_update] * len(n_clicks_list), dash.no_update

        # Extract the button information from the triggered component
        triggered_id = ctx.triggered[0]["prop_id"].split(".")[0]
        import json
        button_data = json.loads(triggered_id)
        clicked_index = button_data["index"]

        # Process the trade execution
        if fib_data:
            try:
                # Check if user is logged in
                if "user_id" not in session:
                    result = ["Please log in to execute trades"]
                    return result + [dash.no_update] * (len(n_clicks_list) - 1), dash.no_update

                user_id = session["user_id"]

                # Get selected exchange connection
                selected_connection_id = account_data.get("connection_id")
                if not selected_connection_id:
                    result = ["Please select an exchange connection"]
                    return result + [dash.no_update] * (len(n_clicks_list) - 1), dash.no_update

                # Get the connection details with user credentials
                connection = ExchangeConnectionDB.get_exchange_connection_by_id(selected_connection_id)

                if not connection or connection.user_id != user_id:
                    result = ["Invalid exchange connection"]
                    return result + [dash.no_update] * (len(n_clicks_list) - 1), dash.no_update

                # Server-side validation and execution
                from services.trade_creation_service import TradeCreationService
                from services.order_execution_service import OrderExecutionService
                from decimal import Decimal

                # Create trade object with full account data
                username = session.get("username", "no_user_found")
                trade = TradeCreationService.create_fibonacci_trade(fib_data, account_data, username)

                # Execute trade through secure backend with user credentials
                execution_result = OrderExecutionService.execute_setup_trade(trade, connection, False)

                if execution_result.success:
                    # Build payload for chart based on actually executed orders
                    def to_float(x):
                        try:
                            return float(x)
                        except Exception:
                            return None
                    executed = getattr(execution_result, "executed_orders", []) or []
                    entries = []
                    stops = []
                    tps = []
                    for o in executed:
                        otype = str(getattr(o, "orderType", "")).lower()
                        item = {
                            "price": to_float(getattr(o, "price", None)),
                            "qty": to_float(getattr(o, "quantity", None)),
                            "side": getattr(getattr(o, "buySell", None), "value", str(getattr(o, "buySell", "")))
                        }
                        if "take" in otype or "tp" in otype:
                            tps.append(item)
                        elif "stop" in otype or "sl" in otype:
                            stops.append(item)
                        else:
                            entries.append(item)
                    import time
                    payload = {
                        "symbol": getattr(trade, "symbol", None),
                        "direction": str(getattr(trade, "direction", "")),
                        "entries": entries,
                        "stop_loss": stops,
                        "take_profits": tps,
                        "timestamp": int(time.time() * 1000)
                    }
                    status = [f"✅ Trade executed successfully! {len(executed)} orders placed."]
                    return status + [dash.no_update] * (len(n_clicks_list) - 1), payload
                else:
                    result = [f"❌ Trade execution failed: {execution_result.error}"]
                    return result + [dash.no_update] * (len(n_clicks_list) - 1), dash.no_update

            except Exception as e:
                result = [f"❌ Error executing trade: {str(e)}"]
                return result + [dash.no_update] * (len(n_clicks_list) - 1), dash.no_update

        return [dash.no_update] * len(n_clicks_list), dash.no_update

    @app.callback(
        Output("trade-setups-store", "data", allow_duplicate=True),
        Input({"type": "trade-status", "index": ALL}, "children"),
        prevent_initial_call=True
    )
    def refresh_trade_setups_after_execution(trade_status_list):
        """Refresh trade setups when a trade is successfully executed"""
        # Check if any status indicates successful execution
        for status in trade_status_list:
            if isinstance(status, str) and "✅ Trade executed successfully" in status:
                return get_trade_setups()

        return dash.no_update
    # Clientside: push executed orders to the chart iframe via postMessage
    app.clientside_callback(
        """
        function(data){
            if(!data){return window.dash_clientside.no_update;}
            try{
                var iframe = document.getElementById('chart-iframe');
                if(iframe && iframe.contentWindow){
                    iframe.contentWindow.postMessage({type: 'display_executed_orders', payload: data}, '*');
                }
            }catch(e){
                console.error('postMessage error', e);
            }
            return window.dash_clientside.no_update;
        }
        """,
        Output('order-dispatch-sink','children'),
        Input('executed-orders-store','data'),
        prevent_initial_call=True
    )


    @app.callback(
        Output("fib-data-output", "children"),
        Output("trade-details-store", "data"),
        Input("fib-event-listener", "n_events"),
        Input("fib-event-listener", "event")
    )
    def handle_fib_completion(n_events, event_data):
        if n_events and event_data:
            # Extract the fib data
            fib_data = event_data.get("detail", {})
            fib_type = fib_data.get("type", "manual")

            if fib_type == "auto-fib":
                print("🤖 Auto-Fib Tool Completed!")
                print(f"📈 Swing Type: {fib_data.get('swingType', 'unknown')}")
            else:
                print("🎯 Manual Fib Tool Completed!")

            print("📊 Price Data:", fib_data)

            if fib_data:
                print(f"P1 (Start): {fib_data.get('p1')}")
                print(f"P2 (End): {fib_data.get('p2')}")
                print(f"50% Level: {fib_data.get('fib50')}")
                print(f"61.8% Level: {fib_data.get('fib618')}")
                print(f"78.6% Level: {fib_data.get('fib786')}")
                print(f"TP 23.6%: {fib_data.get('fibtp236')}")
                print(f"TP 38.2%: {fib_data.get('fibtp382')}")
                print(f"Symbol: {fib_data.get('symbol')}")
                print(f"Timestamp: {fib_data.get('timestamp')}")
                print("=" * 50)

            return f"Fib completed: {fib_data}", fib_data

        return "No fib data yet", {}

    def generate_trade_details_component(fib_data, account_size=10000):
        """Generate trade details Dash component from Fibonacci data"""
        # Ensure account_size is valid
        if account_size <= 0:
            account_size = 10000  # Default fallback

        if not fib_data:
            return dbc.Card([
                dbc.CardHeader([
                    html.Div([
                        html.I(className="bi bi-target me-2", style={"fontSize": "1.2rem", "color": "#6c757d"}),
                        html.H5("Trade Details", className="mb-0 text-muted")
                    ], className="d-flex align-items-center")
                ]),
                dbc.CardBody([
                    html.Div([
                        html.Div([
                            html.I(className="bi bi-crosshair",
                                   style={"fontSize": "3rem", "color": "#6c757d", "opacity": "0.3"}),
                        ], className="text-center mb-3"),
                        html.H6("No Trade Plan Yet",
                                className="text-muted text-center mb-2",
                                style={"fontWeight": "500"}),
                        html.P([
                            "Draw a Fibonacci retracement to generate your trade plan with entries, ",
                            "stop loss, and take profit levels."
                        ], className="text-muted text-center mb-0",
                           style={"fontSize": "0.9rem", "lineHeight": "1.4"})
                    ], className="py-4")
                ])
            ], className="shadow-sm")

        # Extract Fibonacci levels
        p1 = fib_data.get('p1', 0)
        p2 = fib_data.get('p2', 0)
        fib50 = fib_data.get('fib50', 0)
        fib618 = fib_data.get('fib618', 0)
        fib786 = fib_data.get('fib786', 0)
        fibtp236 = fib_data.get('fibtp236', 0)
        fibtp382 = fib_data.get('fibtp382', 0)

        # Determine trade direction
        is_long = p1 < p2
        direction = "LONG" if is_long else "SHORT"
        direction_color = "#28a745" if is_long else "#dc3545"
        direction_icon = "📈" if is_long else "📉"

        # Calculate trade parameters
        stop_loss = p1
        entries = [
            {"level": "50%", "price": fib50, "qty": 0.02},
            {"level": "61.8%", "price": fib618, "qty": 0.02},
            {"level": "78.6%", "price": fib786, "qty": 0.02}
        ]
        take_profits = [
            {"level": "23.6%", "price": fibtp236, "qty": 0.03, "note": "Half position"},
            {"level": "38.2%", "price": fibtp382, "qty": 0.03, "note": "Remaining position"}
        ]

        # Calculate metrics
        total_qty = sum(entry["qty"] for entry in entries)
        avg_entry_price = sum(entry["price"] * entry["qty"] for entry in entries) / total_qty if total_qty > 0 else 0
        risk_per_share = abs(avg_entry_price - stop_loss)
        total_risk = risk_per_share * total_qty
        account_risk = (total_risk / account_size) * 100 if account_size > 0 else 0

        # Calculate profits
        tp1_profit = abs(take_profits[0]["price"] - avg_entry_price) * take_profits[0]["qty"]
        tp2_profit = abs(take_profits[1]["price"] - avg_entry_price) * take_profits[1]["qty"]
        total_potential_profit = tp1_profit + tp2_profit
        risk_reward_ratio = total_potential_profit / total_risk if total_risk > 0 else 0

        return dbc.Card([
            # Header with trade direction and summary
            dbc.CardHeader([
                dbc.Row([
                    dbc.Col([
                        html.Div([
                            html.Span(direction_icon, style={"fontSize": "1.5rem", "marginRight": "8px"}),
                            html.Span(f"{direction} Trade Plan",
                                      style={"fontSize": "1.25rem", "fontWeight": "bold", "color": direction_color})
                        ])
                    ], width=6),
                    dbc.Col([
                        html.Div([
                            dbc.Badge(f"Risk: {account_risk:.2f}% (${total_risk:.2f})",
                                      color="warning", className="me-2"),
                            dbc.Badge(f"R/R: 1:{risk_reward_ratio:.2f}",
                                      color="info", className="me-2"),
                            dbc.Badge(f"Potential: ${total_potential_profit:.2f}",
                                      color="success")
                        ], className="text-end")
                    ], width=6)
                ], align="center")
            ]),

            dbc.CardBody([
                # Stop Loss Section
                dbc.Row([
                    dbc.Col([
                        html.H6("🛑 STOP LOSS", className="text-danger mb-2"),
                        dbc.Card([
                            dbc.CardBody([
                                dbc.Row([
                                    dbc.Col([
                                        html.Span(f"${stop_loss:.2f}",
                                                  style={"fontSize": "1.1rem", "fontWeight": "bold"})
                                    ], width=6),
                                    dbc.Col([
                                        html.Span(f"Risk: ${total_risk:.2f}",
                                                  className="text-danger", style={"fontSize": "0.9rem"})
                                    ], width=6, className="text-end")
                                ])
                            ], className="py-2")
                        ], color="danger", outline=True)
                    ], width=12, className="mb-3")
                ]),

                # Entries Section
                dbc.Row([
                    dbc.Col([
                        html.H6(f"📈 ENTRIES (Total Qty: {total_qty})", className="text-warning mb-2"),
                        html.Div([
                            dbc.Card([
                                dbc.CardBody([
                                    dbc.Row([
                                        dbc.Col([
                                            html.Span(f"{entry['level']}: ${entry['price']:.2f} ({entry['qty']})",
                                                      style={"fontSize": "0.95rem"})
                                        ], width=8),
                                        dbc.Col([
                                            html.Span(f"${entry['price'] * entry['qty']:.2f}",
                                                      className="text-warning", style={"fontSize": "0.9rem"})
                                        ], width=4, className="text-end")
                                    ])
                                ], className="py-2")
                            ], color="warning", outline=True, className="mb-2")
                            for entry in entries
                        ])
                    ], width=12, className="mb-3")
                ]),

                # Take Profits Section
                dbc.Row([
                    dbc.Col([
                        html.H6("🎯 TAKE PROFITS", className="text-success mb-2"),
                        html.Div([
                            dbc.Card([
                                dbc.CardBody([
                                    dbc.Row([
                                        dbc.Col([
                                            html.Span(f"{tp['level']}: ${tp['price']:.2f} ({tp['qty']})",
                                                      style={"fontSize": "0.95rem"})
                                        ], width=8),
                                        dbc.Col([
                                            html.Span(f"+${abs(tp['price'] - avg_entry_price) * tp['qty']:.2f}",
                                                      className="text-success", style={"fontSize": "0.9rem"})
                                        ], width=4, className="text-end")
                                    ])
                                ], className="py-2")
                            ], color="success", outline=True, className="mb-2")
                            for tp in take_profits
                        ])
                    ], width=12, className="mb-3")
                ]),

                # Play Trade Button and Status
                dbc.Row([
                    dbc.Col([
                        dbc.Button([
                            html.I(className="bi bi-play-fill me-2"),
                            "Play Trade"
                        ], id={"type": "play-trade-btn", "index": "main"}, color="success", size="lg",
                            className="w-100"),
                        html.Div(id={"type": "trade-status", "index": "main"}, className="mt-2")
                    ], width=12)
                ])
            ])
        ], className="shadow-sm")

    @app.callback(
        Output("trade-details-container", "children"),
        Input("trade-details-store", "data"),
        State("account-data-store", "data")
    )
    def update_trade_details_display(fib_data, account_data):
        """Update the trade details display when new Fibonacci data is received"""
        account_size = account_data.get("balance", 10000)
        return generate_trade_details_component(fib_data, account_size)

    def generate_trade_setups_component(setups_data):
        """Generate trade setups Dash component from setups data with enhanced visuals"""
        setups = setups_data.get("setups", [])

        if not setups:
            return dbc.Card([
                dbc.CardHeader([
                    html.Div([
                        html.I(className="bi bi-clipboard-data me-2", style={"fontSize": "1.2rem", "color": "#0d6efd"}),
                        html.H5("Trade Setups", className="mb-0 me-2"),
                        dbc.Badge("0", color="primary", className="badge-pulse")
                    ], className="d-flex align-items-center")
                ], className="trade-setups-header"),
                dbc.CardBody([
                    html.Div([
                        html.Div([
                            html.I(className="bi bi-lightbulb", style={"fontSize": "3rem", "color": "#6c757d", "opacity": "0.3"}),
                        ], className="text-center mb-3"),
                        html.H6("No Trade Setups Yet", className="text-muted text-center mb-2", style={"fontWeight": "500"}),
                        html.P("Draw a Fibonacci retracement on the chart to create your first setup.",
                               className="text-muted text-center mb-0", style={"fontSize": "0.9rem", "lineHeight": "1.4"})
                    ], className="py-4")
                ])
            ], className="trade-setups-card shadow-sm")

        # Generate setup cards
        setup_cards = []
        for i, setup in enumerate(setups):
            direction = setup.get("direction", "LONG")
            direction_color = "#28a745" if direction.upper() == "LONG" else "#dc3545"
            direction_icon = "📈" if direction.upper() == "LONG" else "📉"
            symbol = setup.get("symbol", "UNKNOWN")
            entries_text = setup.get("entry_levels", "N/A")
            tps_text = setup.get("tp_levels", "N/A")
            p1 = setup.get("p1", 0.0)
            p2 = setup.get("p2", 0.0)
            exchange = setup.get("exchange", "")
            timeframe = setup.get("timeframe", "")

            setup_card = dbc.Card([
                dbc.CardBody([
                    # Header with symbol, direction and chips
                    dbc.Row([
                        dbc.Col([
                            html.Div([
                                html.Span(symbol, style={"fontSize": "1.05rem", "fontWeight": "700"}),
                                html.Span(f" {direction_icon} {direction}", className="ms-2 direction-pill", style={"color": "white", "background": direction_color, "padding": "0.15rem 0.5rem", "borderRadius": "999px", "fontSize": "0.75rem"}),
                            ], className="d-flex align-items-center")
                        ], width=7),
                        dbc.Col([
                            html.Div([
                                html.Span(exchange, className="chip me-1"),
                                html.Span(timeframe, className="chip me-1"),
                                html.Span(setup.get("type", "Fibonacci"), className="chip chip-info")
                            ], className="text-end")
                        ], width=5)
                    ], className="mb-2"),

                    # Price levels summary with interactive hover tooltips
                    html.Div([
                        html.Small([html.I(className="bi bi-geo-alt-fill me-1 text-muted"), f"P1: {p1:.2f} → P2: {p2:.2f}"], className="kpi-line d-block"),
                        html.Small([
                            html.Span("Entries:", id=f"entries-label-{i}", className="text-muted me-1"),
                            html.Span(entries_text)
                        ], className="kpi-line d-block"),
                        dbc.Tooltip(entries_text, target=f"entries-label-{i}", placement="top", autohide=True),
                        html.Small([
                            html.Span("TPs:", id=f"tps-label-{i}", className="text-muted me-1"),
                            html.Span(tps_text)
                        ], className="kpi-line d-block")
                    ], className="mb-2"),

                    # Timestamp and actions
                    dbc.Row([
                        dbc.Col(html.Small(setup.get("timestamp", ""), className="text-muted"), width=8),
                        dbc.Col(
                            html.Div(
                                dbc.Button(html.I(className="bi bi-x-lg"), id={"type": "remove-setup", "index": i},
                                           color="light", size="sm", className="btn-ghost-icon", n_clicks=0),
                                className="text-end"
                            ), width=4
                        )
                    ])
                ], className="py-2")
            ], className="setup-card mb-2 hover-lift", style={"borderLeft": f"6px solid {direction_color}"})

            setup_cards.append(setup_card)

        return dbc.Card([
            dbc.CardHeader([
                html.Div([
                    html.I(className="bi bi-clipboard-data me-2", style={"fontSize": "1.2rem", "color": "#0d6efd"}),
                    html.H5("Trade Setups", className="mb-0 me-2"),
                    dbc.Badge(len(setups), color="primary", className="badge-pulse")
                ], className="d-flex align-items-center")
            ], className="trade-setups-header"),
            dbc.CardBody(setup_cards)
        ], className="trade-setups-card shadow-sm")

    @app.callback(
        Output("trade-setups-container", "children"),
        Input("trade-setups-store", "data")
    )
    def update_trade_setups_display(setups_data):
        """Update the trade setups display when new setups are added"""
        return generate_trade_setups_component(setups_data)

    # Note: The old update_wallet_balance callback has been replaced by update_balance_display above

    @app.callback(
        Output("risk-display", "children"),
        Output("account-data-store", "data", allow_duplicate=True),
        Input("risk-slider", "value"),
        State("account-data-store", "data"),
        prevent_initial_call=True
    )
    def update_risk_display(risk_percentage, current_data):
        """Update risk display and calculate dollar amount"""

        balance = current_data.get("balance", 0.00)
        risk_amount = balance * (risk_percentage / 100)

        # Update store data
        updated_data = current_data.copy()
        updated_data["risk"] = risk_percentage

        risk_text = f"{risk_percentage}% = ${risk_amount:,.2f}"

        return risk_text, updated_data

    # Note: Exchange selection is now handled by update_balance_display callback

    @app.callback(
        Output("balance-display-content", "children", allow_duplicate=True),
        Input("balance-update-interval", "n_intervals"),
        [State("exchange-dropdown", "value"),
         State("account-data-store", "data")],
        prevent_initial_call=True
    )
    def periodic_balance_update(n_intervals, selected_connection_id, current_data):
        """Periodically update balance in the background"""
        if not selected_connection_id or "user_id" not in session:
            return dash.no_update

        user_id = session["user_id"]

        try:
            # Get the connection details
            connection = ExchangeConnectionDB.get_exchange_connection_by_id(selected_connection_id)

            if not connection or connection.user_id != user_id:
                return dash.no_update

            # Fetch balance quietly (no loading state for periodic updates)
            balance_result = get_exchange_balance(connection)

            if balance_result["success"]:
                # Success - show updated balance
                balance_content = [
                    html.Span("$", className="text-muted me-1"),
                    html.Span(f"{balance_result['balance']:,.2f}",
                              className="fw-bold", style={"fontSize": "1.1rem"}),
                    html.Small(f" {balance_result['currency']}", className="text-muted ms-1"),
                    html.Small(" (auto-updated)", className="text-muted ms-2", style={"fontSize": "0.7rem"})
                ]
                return balance_content
            else:
                # Don't update on error during periodic refresh
                return dash.no_update

        except Exception as e:
            print(f"❌ Error in periodic balance update: {e}")
            return dash.no_update

    @app.callback(
        Output("trade-setups-store", "data", allow_duplicate=True),
        Input({"type": "remove-setup", "index": ALL}, "n_clicks"),
        State("trade-setups-store", "data"),
        prevent_initial_call=True
    )
    def remove_trade_setup(n_clicks_list, setups_data):
        """Remove a trade setup when the × button is clicked"""
        if not any(n_clicks_list):
            return setups_data

        # Find which button was clicked
        ctx = dash.callback_context
        if not ctx.triggered:
            return setups_data

        # Extract the index from the triggered button
        button_id = ctx.triggered[0]["prop_id"].split(".")[0]
        import json
        button_data = json.loads(button_id)
        index_to_remove = button_data["index"]

        # Remove the setup at the specified index
        updated_setups = setups_data.copy()
        current_setups = updated_setups.get("setups", [])

        if 0 <= index_to_remove < len(current_setups):
            removed_setup = current_setups.pop(index_to_remove)
            print(
                f"🗑️ Removed trade setup: {removed_setup.get('symbol', 'UNKNOWN')} {removed_setup.get('direction', '')}")
            updated_setups["setups"] = current_setups

        return updated_setups


get_callbacks(app)


def run_fastapi():
    """Run FastAPI in a separate thread"""
    uvicorn.run(
        "services.chart_api:app",
        host="0.0.0.0",
        port=8900,
        reload=False,  # Disable reload in threaded mode
        log_level="debug"
    )


def run_dash():
    """Run Dash in the main thread"""
    time.sleep(2)  # Give FastAPI time to start
    app.run(debug=False, port=8050)


app.server.secret_key = os.urandom(24)  # generates a random key each run
if __name__ == "__main__":
    # Start FastAPI in background thread
    fastapi_thread = threading.Thread(target=run_fastapi, daemon=True)
    fastapi_thread.start()

    # Run Dash in the main thread
    run_dash()


def update_btc_candles(_, stored_y_range):
    from services import binance as binance_service

    # Fetch 1-hour interval candlesticks for BTC/USDT
    df = binance_service.get_candlestick_dataframe(symbol="BTCUSDT", limit=1000)
